api_key=BwaKVfVUr6vY0Q3ZjIghw26DEQbnxq3gifggupOuNNdmFx5MhragJQQJ99BAACHYHv6XJ3w3AAAAACOGH2Dp
PROJECT_CONNECTION_STRING=eastus2.api.azureml.ms;1971b124-6566-4384-ad06-3336db84d187;rg10agent;amitlal-agentic-project-sap
BING_CONNECTION_NAME=bingamit
BING_CONNECTION_NAME=bing
BING_API_KEY=********************************
BING_API_KEY=********************************
BING_API_KEY=********************************
MODEL_DEPLOYMENT_NAME=gpt-4o
MODEL_API_VERSION=2025-01-01-preview
#MODEL_API_VERSION=2024-02-15-preview
AZURE_ENDPOINT=https://amithub3822592262.openai.azure.com
CURRENTS_API_KEY =k-KRJqCZvvNfJOOmlB4b7H-8adhTrb4IoKcHXW1IECFPnUra
# SAP ECC Connection Details
# ✅ SAP OData API Credentials
SAP_USERNAME=S0021789894
SAP_PASSWORD=Canada#213141
SAP_SERVER=https://sapes5.sapdevcenter.com/
SAP_ODATA_USER=S0021789894
SAP_ODATA_PASSWORD=Canada#213141
#SAP_CLIENT=002
SAP_LANGUAGE=EN
MAIL_RECIPIENT="<EMAIL>"
EMAIL_RECIPIENT="<EMAIL>"
LOGICAPP_ENDPOINT="https://prod-45.eastus2.logic.azure.com:443/workflows/c6d16626ac62471ba55b3070827ba619/triggers/When_a_HTTP_request_is_received/paths/invoke?api-version=2016-10-01&sp=%2Ftriggers%2FWhen_a_HTTP_request_is_received%2Frun&sv=1.0&sig=hdF5CA7cf648hgkjMOqt9ygNGX3Q784p_fxtz8pSijY"
#LOGICAPP_ENDPOINT2="https://prod-26.eastus2.logic.azure.com:443/workflows/0a8dc9b8ba71422c96d7845a33f31078/triggers/When_a_HTTP_request_is_received/paths/invoke?api-version=2016-10-01&sp=%2Ftriggers%2FWhen_a_HTTP_request_is_received%2Frun&sv=1.0&sig=pt7I1A1x0Xw1BtThdxuezE3lpJ45N6M_o2Fb9sMS0Go"
#ALPHAVANTAGE_API_KEY=B10L7AIMBJVXF2NI
ALPHAVANTAGE_API_KEY=62YKDSM4M6CALOTA
Ocp-Apim-Subscription-Key=5e73398b7f5b4138b11fe8363e9c1bb2
# OpenAI API Key for GPT-4
OPENAI_API_KEY=********************************************************************************************************************************************************************