#!/usr/bin/env python3
"""
Test Script for Enhanced Transit Scenario Copilot
================================================

Quick test to verify all improvements are working correctly.
"""

import sys
import os
from pathlib import Path

def test_imports():
    """Test that all required imports work correctly"""
    print("🔍 Testing imports...")
    
    try:
        import pandas as pd
        import numpy as np
        import gradio as gr
        import plotly.graph_objects as go
        import plotly.express as px
        from plotly.subplots import make_subplots
        print("✅ Core libraries imported successfully")
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    
    try:
        from transit_scenario_copilot import Config, create_dashboard
        print("✅ Transit copilot modules imported successfully")
    except ImportError as e:
        print(f"❌ Transit copilot import error: {e}")
        return False
    
    return True

def test_config():
    """Test the enhanced configuration"""
    print("\n🔧 Testing enhanced configuration...")
    
    try:
        from transit_scenario_copilot import Config
        config = Config()
        
        # Test new color palette
        assert 'primary_light' in config.colors
        assert 'background_dark' in config.colors
        assert 'success' in config.colors
        print("✅ Enhanced color palette loaded")
        
        # Test new performance settings
        assert hasattr(config, 'max_concurrent_agents')
        assert hasattr(config, 'enable_animations')
        assert hasattr(config, 'chart_config')
        print("✅ Performance and demo settings configured")
        
        return True
    except Exception as e:
        print(f"❌ Config test error: {e}")
        return False

def test_dashboard_creation():
    """Test that the enhanced dashboard can be created"""
    print("\n🎨 Testing enhanced dashboard creation...")
    
    try:
        from transit_scenario_copilot import Config, create_dashboard
        config = Config()
        
        # Create dashboard (don't launch)
        dashboard = create_dashboard(config)
        print("✅ Enhanced dashboard created successfully")
        
        return True
    except Exception as e:
        print(f"❌ Dashboard creation error: {e}")
        return False

def test_helper_functions():
    """Test new helper functions"""
    print("\n🛠️ Testing helper functions...")
    
    try:
        from transit_scenario_copilot import (
            create_real_time_status_indicator,
            create_enhanced_dashboard_css,
            Config
        )
        
        config = Config()
        
        # Test status indicator
        status_html = create_real_time_status_indicator(config)
        assert "LIVE DEMO ACTIVE" in status_html
        assert "pulse" in status_html
        print("✅ Real-time status indicator working")
        
        # Test enhanced CSS
        css = create_enhanced_dashboard_css(config)
        assert "gradient" in css
        assert "animation" in css
        assert "hover" in css
        print("✅ Enhanced CSS generation working")
        
        return True
    except Exception as e:
        print(f"❌ Helper functions test error: {e}")
        return False

def test_sample_data():
    """Test that sample data files exist"""
    print("\n📊 Testing sample data availability...")
    
    sample_files = [
        "upt_prediction3.csv",
        "requirements_transit.txt"
    ]
    
    all_exist = True
    for file in sample_files:
        if Path(file).exists():
            print(f"✅ {file} found")
        else:
            print(f"⚠️ {file} not found (optional)")
    
    return True

def run_comprehensive_test():
    """Run all tests"""
    print("🚀 Starting Enhanced Transit Copilot Test Suite")
    print("=" * 50)
    
    tests = [
        ("Import Test", test_imports),
        ("Configuration Test", test_config),
        ("Dashboard Creation Test", test_dashboard_creation),
        ("Helper Functions Test", test_helper_functions),
        ("Sample Data Test", test_sample_data)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Enhanced Transit Copilot is ready for demo.")
        print("\n🚀 To launch the enhanced demo, run:")
        print("   python transit_scenario_copilot.py")
    else:
        print("⚠️ Some tests failed. Please check the issues above.")
        print("\n🔧 Common solutions:")
        print("   - Install missing dependencies: pip install -r requirements_transit.txt")
        print("   - Check file paths and permissions")
        print("   - Verify Python environment setup")
    
    return passed == total

if __name__ == "__main__":
    success = run_comprehensive_test()
    sys.exit(0 if success else 1)
