#!/usr/bin/env python3
"""
Test Script for Revamped Transit Scenario Copilot
================================================

Quick test to verify the revamped system works correctly.
"""

import sys
import os
import time
from pathlib import Path

def test_heartbeat_system():
    """Test the heartbeat monitoring system"""
    print("\n💓 Testing Heartbeat System...")
    
    try:
        from transit_scenario_copilot import SYSTEM_STATUS
        
        # Test status updates
        SYSTEM_STATUS.update_agent_status('data_analyst', 'processing')
        assert SYSTEM_STATUS.agent_states['data_analyst'] == 'processing'
        print("✅ Agent status updates working")
        
        # Test heartbeat HTML generation
        heartbeat_html = SYSTEM_STATUS.get_heartbeat_html()
        print(f"Heartbeat HTML length: {len(heartbeat_html)}")
        print(f"Full heartbeat HTML: {heartbeat_html}")

        # Check for key components
        has_system_ready = "System Ready" in heartbeat_html
        has_agent_active = "Agent Active" in heartbeat_html
        has_system_error = "System Error" in heartbeat_html
        has_animation = "animation" in heartbeat_html
        has_style = "style" in heartbeat_html

        print(f"Has 'System Ready': {has_system_ready}")
        print(f"Has 'Agent Active': {has_agent_active}")
        print(f"Has 'System Error': {has_system_error}")
        print(f"Has 'animation': {has_animation}")
        print(f"Has 'style': {has_style}")

        # Should have one of the system status messages
        has_status = has_system_ready or has_agent_active or has_system_error
        assert has_status, "Heartbeat HTML should contain a status message"
        assert has_animation, "Heartbeat HTML should contain 'animation'"
        print("✅ Heartbeat HTML generation working")
        
        # Reset status
        SYSTEM_STATUS.update_agent_status('data_analyst', 'idle')
        
        return True
    except Exception as e:
        print(f"❌ Heartbeat system test failed: {e}")
        return False

def test_config_system():
    """Test the revamped configuration"""
    print("\n⚙️ Testing Configuration System...")
    
    try:
        from transit_scenario_copilot import Config
        config = Config()
        
        # Test APTA-aligned colors
        assert 'apta_blue' in config.colors
        assert 'transit_blue' in config.colors
        assert 'active' in config.colors
        print("✅ APTA-aligned color palette loaded")
        
        # Test simplified settings
        assert hasattr(config, 'agent_timeout')
        assert hasattr(config, 'heartbeat_interval')
        assert config.agent_timeout == 20
        print("✅ Simplified agent settings configured")
        
        return True
    except Exception as e:
        print(f"❌ Config system test failed: {e}")
        return False

def test_agent_functions():
    """Test that agent functions can be imported"""
    print("\n🤖 Testing Agent Functions...")
    
    try:
        from transit_scenario_copilot import (
            ridership_analytics_agent,
            simulation_agent,
            recommendation_agent,
            call_llm
        )
        print("✅ Core agent functions imported")
        
        # Test LLM call with mock
        response = call_llm("Test prompt", None)
        assert isinstance(response, str)
        print("✅ LLM call function working")
        
        return True
    except Exception as e:
        print(f"❌ Agent functions test failed: {e}")
        return False

def test_dashboard_creation():
    """Test dashboard creation without launching"""
    print("\n🎨 Testing Dashboard Creation...")
    
    try:
        from transit_scenario_copilot import Config, create_dashboard
        config = Config()
        
        # This should create the dashboard object without launching
        dashboard = create_dashboard(config)
        print("✅ Dashboard created successfully")
        
        return True
    except Exception as e:
        print(f"❌ Dashboard creation test failed: {e}")
        return False

def test_data_processing():
    """Test basic data processing functions"""
    print("\n📊 Testing Data Processing...")
    
    try:
        from transit_scenario_copilot import parse_scenario_text
        
        # Test scenario parsing
        test_scenario = "increase rail frequency 20% off-peak"
        parsed = parse_scenario_text(test_scenario)
        
        if parsed:
            print("✅ Scenario parsing working")
        else:
            print("⚠️ Scenario parsing returned empty (may be expected)")
        
        return True
    except Exception as e:
        print(f"❌ Data processing test failed: {e}")
        return False

def run_comprehensive_test():
    """Run all tests for the revamped system"""
    print("🚀 Starting Revamped Transit Copilot Test Suite")
    print("=" * 55)
    
    tests = [
        ("Heartbeat System", test_heartbeat_system),
        ("Configuration System", test_config_system),
        ("Agent Functions", test_agent_functions),
        ("Dashboard Creation", test_dashboard_creation),
        ("Data Processing", test_data_processing)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
    
    print("\n" + "=" * 55)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Revamped Transit Copilot is ready!")
        print("\n🚀 Key improvements verified:")
        print("   ✅ Heartbeat monitoring system")
        print("   ✅ APTA-aligned design colors")
        print("   ✅ Simplified agent configuration")
        print("   ✅ Robust error handling")
        print("   ✅ Enhanced dashboard creation")
        print("\n🎯 To launch the revamped demo:")
        print("   python transit_scenario_copilot.py")
        print("\n💡 Features to try:")
        print("   • Watch the heartbeat indicator")
        print("   • Use predefined dropdown examples")
        print("   • Test agent collaboration")
        print("   • Observe status updates")
    else:
        print("⚠️ Some tests failed. Issues to check:")
        print("   - Missing dependencies")
        print("   - Import errors")
        print("   - Configuration problems")
        print("\n🔧 Quick fixes:")
        print("   pip install gradio plotly pandas numpy")
        print("   pip install python-dotenv")
    
    return passed == total

def demo_heartbeat():
    """Demonstrate the heartbeat system"""
    print("\n💓 Heartbeat Demo:")
    print("Simulating agent activity...")
    
    try:
        from transit_scenario_copilot import SYSTEM_STATUS
        
        agents = ['data_analyst', 'scenario_modeler', 'strategy_advisor', 'insights_agent']
        statuses = ['processing', 'thinking', 'idle', 'processing']
        
        for i, (agent, status) in enumerate(zip(agents, statuses)):
            SYSTEM_STATUS.update_agent_status(agent, status)
            print(f"  {i+1}. {agent}: {status}")
            time.sleep(0.5)
        
        print("\nGenerating heartbeat HTML...")
        heartbeat = SYSTEM_STATUS.get_heartbeat_html()
        print("✅ Heartbeat system working!")
        
        # Reset all to idle
        for agent in agents:
            SYSTEM_STATUS.update_agent_status(agent, 'idle')
            
    except Exception as e:
        print(f"❌ Heartbeat demo failed: {e}")

if __name__ == "__main__":
    success = run_comprehensive_test()
    
    if success:
        demo_heartbeat()
    
    sys.exit(0 if success else 1)
