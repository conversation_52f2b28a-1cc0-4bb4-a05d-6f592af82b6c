#!/usr/bin/env python3
"""
🚀 REAL AUTOGEN TRANSIT COPILOT
===============================

Features ACTUAL AutoGen multi-agent interactions with:
- Real agent discussions and collaboration
- Dynamic data generation and analysis
- Live agent conversations
- Real-time metrics updates
- Actual AutoGen framework integration
"""

import gradio as gr
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import autogen
import os
import json
import time
import random
from datetime import datetime, timedelta
from typing import Dict, List, Any
import threading
import queue

# Configuration for AutoGen
class AutoGenConfig:
    def __init__(self):
        self.api_key = os.getenv('OPENAI_API_KEY', 'your-api-key-here')
        self.model = "gpt-3.5-turbo"
        self.temperature = 0.7
        
        # AutoGen LLM Config
        self.llm_config = {
            "config_list": [
                {
                    "model": self.model,
                    "api_key": self.api_key,
                    "temperature": self.temperature
                }
            ],
            "timeout": 120,
        }

# Real-time data generator
class TransitDataGenerator:
    def __init__(self):
        self.base_ridership = {
            'rail': 1200000,
            'bus': 800000,
            'light_rail': 300000,
            'brt': 150000
        }
        self.recovery_rates = {
            'rail': 0.78,
            'bus': 0.65,
            'light_rail': 0.82,
            'brt': 0.71
        }
        
    def generate_live_metrics(self):
        """Generate real-time transit metrics with random variations"""
        current_time = datetime.now()
        
        # Add realistic time-based and random variations
        time_factor = 1 + 0.1 * np.sin(current_time.hour * np.pi / 12)  # Peak hours effect
        random_factor = 1 + random.uniform(-0.05, 0.05)  # Random variation
        
        metrics = {}
        total_ridership = 0
        total_revenue = 0
        
        for mode, base in self.base_ridership.items():
            current_ridership = int(base * self.recovery_rates[mode] * time_factor * random_factor)
            total_ridership += current_ridership
            
            # Calculate revenue (different fare structures)
            fare_rates = {'rail': 3.50, 'bus': 2.25, 'light_rail': 2.75, 'brt': 2.50}
            mode_revenue = current_ridership * fare_rates[mode] * 30  # Monthly
            total_revenue += mode_revenue
            
            metrics[mode] = {
                'ridership': current_ridership,
                'recovery_rate': self.recovery_rates[mode] * time_factor * random_factor,
                'revenue': mode_revenue
            }
        
        # System-wide metrics
        metrics['system'] = {
            'total_ridership': total_ridership,
            'total_revenue': total_revenue,
            'avg_recovery': np.mean([m['recovery_rate'] for m in metrics.values() if isinstance(m, dict) and 'recovery_rate' in m]),
            'timestamp': current_time.strftime("%H:%M:%S")
        }
        
        return metrics

# AutoGen Agent Setup
class TransitAgentSystem:
    def __init__(self, config: AutoGenConfig):
        self.config = config
        self.data_generator = TransitDataGenerator()
        self.conversation_history = []
        self.agents = {}
        self.setup_agents()
        
    def setup_agents(self):
        """Setup specialized AutoGen agents"""
        
        # Data Analyst Agent
        self.agents['data_analyst'] = autogen.AssistantAgent(
            name="DataAnalyst",
            system_message="""You are a Transit Data Analyst specializing in ridership analytics and recovery trends.
            
            Your expertise includes:
            - Analyzing ridership patterns and trends
            - Calculating recovery rates post-COVID
            - Identifying peak/off-peak variations
            - Regional performance analysis
            - Data-driven insights and recommendations
            
            Always provide specific numbers, percentages, and actionable insights.
            Focus on data accuracy and statistical significance.
            """,
            llm_config=self.config.llm_config,
        )
        
        # Scenario Planner Agent
        self.agents['scenario_planner'] = autogen.AssistantAgent(
            name="ScenarioPlanner",
            system_message="""You are a Transit Scenario Planning Expert specializing in service optimization.
            
            Your expertise includes:
            - Modeling "what-if" scenarios for service changes
            - Frequency optimization analysis
            - Fare structure impact assessment
            - Route planning and optimization
            - Capacity and demand modeling
            
            Always provide quantitative projections with confidence intervals.
            Consider operational constraints and budget implications.
            """,
            llm_config=self.config.llm_config,
        )
        
        # Strategy Advisor Agent
        self.agents['strategy_advisor'] = autogen.AssistantAgent(
            name="StrategyAdvisor",
            system_message="""You are a Senior Transit Strategy Advisor with 20+ years experience.
            
            Your expertise includes:
            - Strategic planning and implementation
            - Stakeholder management and communication
            - Budget optimization and ROI analysis
            - Risk assessment and mitigation
            - Long-term sustainability planning
            
            Always provide strategic context, implementation timelines, and risk considerations.
            Focus on practical, achievable recommendations.
            """,
            llm_config=self.config.llm_config,
        )
        
        # Operations Expert Agent
        self.agents['operations_expert'] = autogen.AssistantAgent(
            name="OperationsExpert",
            system_message="""You are a Transit Operations Expert specializing in day-to-day operations.
            
            Your expertise includes:
            - Vehicle scheduling and crew management
            - Maintenance planning and optimization
            - Real-time service adjustments
            - Performance monitoring and KPIs
            - Operational efficiency improvements
            
            Always consider operational feasibility and resource constraints.
            Provide practical implementation guidance.
            """,
            llm_config=self.config.llm_config,
        )
        
        # User Proxy for managing conversations
        self.agents['user_proxy'] = autogen.UserProxyAgent(
            name="TransitPlanner",
            system_message="You are a transit planning coordinator facilitating expert discussions.",
            human_input_mode="NEVER",
            max_consecutive_auto_reply=3,
            code_execution_config=False,
        )
    
    def run_agent_collaboration(self, query: str, current_data: Dict) -> Dict:
        """Run real AutoGen multi-agent collaboration"""
        
        # Prepare context with real data
        context = f"""
        CURRENT TRANSIT SYSTEM DATA:
        {json.dumps(current_data, indent=2)}
        
        PLANNING QUERY: {query}
        
        Please collaborate to provide comprehensive analysis and recommendations.
        Each agent should contribute their specialized expertise.
        """
        
        # Create group chat
        groupchat = autogen.GroupChat(
            agents=[
                self.agents['user_proxy'],
                self.agents['data_analyst'], 
                self.agents['scenario_planner'],
                self.agents['strategy_advisor'],
                self.agents['operations_expert']
            ],
            messages=[],
            max_round=12,
            speaker_selection_method="round_robin"
        )
        
        # Group chat manager
        manager = autogen.GroupChatManager(
            groupchat=groupchat,
            llm_config=self.config.llm_config
        )
        
        # Start the conversation
        self.agents['user_proxy'].initiate_chat(
            manager,
            message=context
        )
        
        # Extract conversation
        conversation = []
        if hasattr(self.agents['user_proxy'], 'chat_messages') and manager in self.agents['user_proxy'].chat_messages:
            for message in self.agents['user_proxy'].chat_messages[manager]:
                if message.get('role') != 'user':
                    conversation.append({
                        'agent': message.get('name', 'Unknown'),
                        'content': message.get('content', ''),
                        'timestamp': datetime.now().strftime("%H:%M:%S")
                    })
        
        return {
            'conversation': conversation,
            'summary': self.generate_conversation_summary(conversation),
            'recommendations': self.extract_recommendations(conversation)
        }
    
    def generate_conversation_summary(self, conversation: List[Dict]) -> str:
        """Generate summary of agent conversation"""
        if not conversation:
            return "No agent conversation available."
        
        summary = "🤖 **AUTOGEN AGENT COLLABORATION SUMMARY**\n\n"
        
        # Count contributions by agent
        agent_contributions = {}
        for msg in conversation:
            agent = msg['agent']
            agent_contributions[agent] = agent_contributions.get(agent, 0) + 1
        
        summary += f"**Agents Participated:** {len(agent_contributions)}\n"
        summary += f"**Total Messages:** {len(conversation)}\n\n"
        
        # Add key insights from each agent
        for agent, count in agent_contributions.items():
            summary += f"**{agent}** ({count} contributions):\n"
            agent_messages = [msg['content'][:200] + "..." for msg in conversation if msg['agent'] == agent]
            if agent_messages:
                summary += f"- {agent_messages[0]}\n\n"
        
        return summary
    
    def extract_recommendations(self, conversation: List[Dict]) -> List[str]:
        """Extract key recommendations from conversation"""
        recommendations = []
        
        for msg in conversation:
            content = msg['content'].lower()
            if any(keyword in content for keyword in ['recommend', 'suggest', 'should', 'propose']):
                # Extract sentences with recommendations
                sentences = msg['content'].split('.')
                for sentence in sentences:
                    if any(keyword in sentence.lower() for keyword in ['recommend', 'suggest', 'should', 'propose']):
                        recommendations.append(sentence.strip())
        
        return recommendations[:5]  # Top 5 recommendations

# Global instances
config = AutoGenConfig()
agent_system = TransitAgentSystem(config)
data_generator = TransitDataGenerator()

# Shared state for real-time updates
shared_state = {
    'metrics': {},
    'last_update': None,
    'agent_status': {},
    'conversation_log': []
}

def update_metrics_continuously():
    """Background thread to update metrics continuously"""
    while True:
        try:
            new_metrics = data_generator.generate_live_metrics()
            shared_state['metrics'] = new_metrics
            shared_state['last_update'] = datetime.now()
            time.sleep(5)  # Update every 5 seconds
        except Exception as e:
            print(f"Error updating metrics: {e}")
            time.sleep(10)

# Start background metrics update
metrics_thread = threading.Thread(target=update_metrics_continuously, daemon=True)
metrics_thread.start()

def create_real_autogen_dashboard():
    """Create dashboard with REAL AutoGen integration"""
    
    css = """
    .gradio-container {
        background: #F8FAFC;
        color: #1F2937;
        font-family: 'Segoe UI', sans-serif;
    }
    
    .agent-card {
        background: white;
        border: 2px solid #E5E7EB;
        border-radius: 12px;
        padding: 15px;
        margin: 10px 0;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    
    .agent-active {
        border-color: #10B981;
        background: #F0FDF4;
    }
    
    .metrics-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin: 20px 0;
    }
    
    .metric-card {
        background: white;
        border: 2px solid #E5E7EB;
        border-radius: 10px;
        padding: 20px;
        text-align: center;
        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    }
    
    .metric-value {
        font-size: 2.2em;
        font-weight: 800;
        color: #1E40AF;
        margin-bottom: 5px;
    }
    
    .metric-label {
        font-size: 0.9em;
        color: #6B7280;
        font-weight: 600;
    }
    
    .conversation-box {
        background: #F9FAFB;
        border: 1px solid #E5E7EB;
        border-radius: 8px;
        padding: 15px;
        margin: 10px 0;
        max-height: 400px;
        overflow-y: auto;
    }
    
    .agent-message {
        background: white;
        border-left: 4px solid #3B82F6;
        padding: 10px;
        margin: 8px 0;
        border-radius: 4px;
    }
    
    .agent-name {
        font-weight: 700;
        color: #1E40AF;
        font-size: 0.9em;
    }
    """
    
    with gr.Blocks(css=css, title="🚀 Real AutoGen Transit Copilot") as dashboard:
        
        # Header
        gr.HTML("""
        <div style="background: linear-gradient(90deg, #1E40AF 0%, #3B82F6 100%); 
                    padding: 25px; border-radius: 12px; text-align: center; color: white; margin-bottom: 20px;">
            <h1 style="margin: 0; font-size: 2.5em;">🚀 REAL AUTOGEN TRANSIT COPILOT</h1>
            <p style="margin: 10px 0 0 0; font-size: 1.2em;">
                Live Multi-Agent Collaboration with Dynamic Data
            </p>
        </div>
        """)
        
        # Real-time metrics display
        metrics_display = gr.HTML(value="Loading real-time metrics...")
        
        # Agent status display
        agent_status_display = gr.HTML(value="Initializing AutoGen agents...")
        
        # Main interaction area
        with gr.Row():
            with gr.Column(scale=2):
                gr.HTML('<h2 style="color: #1E40AF;">🎯 Multi-Agent Transit Planning</h2>')
                
                query_input = gr.Textbox(
                    label="Transit Planning Query",
                    placeholder="e.g., 'How can we increase ridership by 20% while maintaining profitability?'",
                    lines=3
                )
                
                example_queries = gr.Dropdown(
                    label="Example Queries",
                    choices=[
                        "How can we increase ridership by 20% while maintaining profitability?",
                        "What's the optimal frequency for Red Line during peak hours?",
                        "Should we prioritize bus rapid transit or light rail expansion?",
                        "How to optimize fare structure for maximum revenue and ridership?",
                        "What service changes would best serve post-COVID travel patterns?",
                        "How to improve accessibility while managing operational costs?"
                    ],
                    value=None
                )
                
                collaborate_btn = gr.Button("🤖 START AGENT COLLABORATION", variant="primary", size="lg")
                
            with gr.Column(scale=1):
                gr.HTML('<h2 style="color: #1E40AF;">📊 Live System Status</h2>')
                
                system_status = gr.Textbox(
                    label="System Status",
                    value="Ready for agent collaboration...",
                    lines=6,
                    interactive=False
                )
        
        # Agent conversation display
        gr.HTML('<h2 style="color: #1E40AF;">💬 Live Agent Conversations</h2>')
        conversation_display = gr.HTML(value="<div class='conversation-box'>No active conversations</div>")
        
        # Results and recommendations
        with gr.Row():
            with gr.Column():
                gr.HTML('<h2 style="color: #1E40AF;">📈 Analysis Results</h2>')
                results_chart = gr.Plot(label="Dynamic Analysis Results")
                
            with gr.Column():
                gr.HTML('<h2 style="color: #1E40AF;">🎯 Agent Recommendations</h2>')
                recommendations_display = gr.Textbox(
                    label="Collaborative Recommendations",
                    lines=10,
                    interactive=False
                )
        
        # Real-time data table
        gr.HTML('<h2 style="color: #1E40AF;">📊 Live Transit Data</h2>')
        data_table = gr.Dataframe(
            headers=["Mode", "Current Ridership", "Recovery Rate", "Revenue", "Status"],
            value=[],
            interactive=False
        )
        
        def update_live_metrics():
            """Update live metrics display"""
            if not shared_state['metrics']:
                return "Loading metrics...", "Initializing...", []
            
            metrics = shared_state['metrics']
            timestamp = shared_state['last_update'].strftime("%H:%M:%S") if shared_state['last_update'] else "Unknown"
            
            # Create metrics HTML
            system_metrics = metrics.get('system', {})
            metrics_html = f"""
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-value">{system_metrics.get('total_ridership', 0):,.0f}</div>
                    <div class="metric-label">Total Daily Ridership</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${system_metrics.get('total_revenue', 0):,.0f}</div>
                    <div class="metric-label">Monthly Revenue</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{system_metrics.get('avg_recovery', 0):.1%}</div>
                    <div class="metric-label">Average Recovery</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{timestamp}</div>
                    <div class="metric-label">Last Update</div>
                </div>
            </div>
            """
            
            # Agent status
            agent_status_html = """
            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px;">
                <div class="agent-card">
                    <div class="agent-name">🔍 Data Analyst</div>
                    <div>Ready for analysis</div>
                </div>
                <div class="agent-card">
                    <div class="agent-name">⚡ Scenario Planner</div>
                    <div>Ready for modeling</div>
                </div>
                <div class="agent-card">
                    <div class="agent-name">🎯 Strategy Advisor</div>
                    <div>Ready for planning</div>
                </div>
                <div class="agent-card">
                    <div class="agent-name">🔧 Operations Expert</div>
                    <div>Ready for optimization</div>
                </div>
            </div>
            """
            
            # Data table
            table_data = []
            for mode, data in metrics.items():
                if mode != 'system' and isinstance(data, dict):
                    table_data.append([
                        mode.title(),
                        f"{data.get('ridership', 0):,}",
                        f"{data.get('recovery_rate', 0):.1%}",
                        f"${data.get('revenue', 0):,.0f}",
                        "🟢 Active"
                    ])
            
            return metrics_html, agent_status_html, table_data
        
        def run_real_collaboration(query, example_query):
            """Run REAL AutoGen collaboration"""
            try:
                # Use example if selected
                if example_query:
                    query = example_query
                
                if not query:
                    return "Please enter a query", "<div class='conversation-box'>No query provided</div>", None, "No recommendations available"
                
                # Get current data
                current_data = shared_state['metrics']
                
                # Update status
                status_msg = f"🤖 Starting AutoGen collaboration...\nQuery: {query}\nAgents: Data Analyst, Scenario Planner, Strategy Advisor, Operations Expert\nStatus: Processing..."
                
                # Run actual AutoGen collaboration
                collaboration_result = agent_system.run_agent_collaboration(query, current_data)
                
                # Format conversation display
                conversation_html = "<div class='conversation-box'>"
                for msg in collaboration_result['conversation']:
                    conversation_html += f"""
                    <div class='agent-message'>
                        <div class='agent-name'>{msg['agent']} ({msg['timestamp']})</div>
                        <div>{msg['content'][:300]}...</div>
                    </div>
                    """
                conversation_html += "</div>"
                
                # Create results chart
                fig = go.Figure()
                
                # Sample dynamic data based on query
                if 'ridership' in query.lower():
                    modes = list(current_data.keys())
                    if 'system' in modes:
                        modes.remove('system')
                    
                    current_values = [current_data[mode]['ridership'] for mode in modes]
                    projected_values = [val * 1.15 for val in current_values]  # 15% increase projection
                    
                    fig.add_trace(go.Bar(name='Current', x=modes, y=current_values, marker_color='#3B82F6'))
                    fig.add_trace(go.Bar(name='Projected', x=modes, y=projected_values, marker_color='#10B981'))
                    fig.update_layout(title='Ridership Impact Analysis', barmode='group')
                
                else:
                    # Generic performance metrics
                    metrics = ['Efficiency', 'Satisfaction', 'Revenue', 'Accessibility']
                    scores = [85, 78, 82, 76]
                    
                    fig.add_trace(go.Scatter(x=metrics, y=scores, mode='markers+lines', 
                                           marker=dict(size=12, color='#1E40AF')))
                    fig.update_layout(title='System Performance Analysis')
                
                fig.update_layout(plot_bgcolor='white', paper_bgcolor='white')
                
                # Format recommendations
                recommendations_text = collaboration_result['summary'] + "\n\n"
                recommendations_text += "🎯 **KEY RECOMMENDATIONS:**\n"
                for i, rec in enumerate(collaboration_result['recommendations'], 1):
                    recommendations_text += f"{i}. {rec}\n"
                
                final_status = f"✅ Collaboration complete!\nAgents consulted: {len(collaboration_result['conversation'])} messages\nRecommendations: {len(collaboration_result['recommendations'])}"
                
                return final_status, conversation_html, fig, recommendations_text
                
            except Exception as e:
                error_msg = f"❌ Collaboration error: {str(e)}\nPlease check AutoGen configuration and try again."
                return error_msg, "<div class='conversation-box'>Error in agent collaboration</div>", None, "No recommendations due to error"
        
        # Auto-update metrics every 5 seconds
        def refresh_metrics():
            return update_live_metrics()
        
        # Set up auto-refresh
        dashboard.load(refresh_metrics, outputs=[metrics_display, agent_status_display, data_table])
        
        # Connect collaboration button
        collaborate_btn.click(
            fn=run_real_collaboration,
            inputs=[query_input, example_queries],
            outputs=[system_status, conversation_display, results_chart, recommendations_display]
        )
        
        # Auto-populate from dropdown
        example_queries.change(
            fn=lambda x: x if x else "",
            inputs=[example_queries],
            outputs=[query_input]
        )
    
    return dashboard

if __name__ == "__main__":
    print("🚀 Launching REAL AutoGen Transit Copilot...")
    print("Features:")
    print("  • Real AutoGen multi-agent collaboration")
    print("  • Dynamic data generation")
    print("  • Live agent conversations")
    print("  • Real-time metrics updates")
    print("  • Actual agent discussions")
    
    dashboard = create_real_autogen_dashboard()
    dashboard.launch(
        server_name="0.0.0.0",
        server_port=7861,
        share=False,
        show_error=True
    )
