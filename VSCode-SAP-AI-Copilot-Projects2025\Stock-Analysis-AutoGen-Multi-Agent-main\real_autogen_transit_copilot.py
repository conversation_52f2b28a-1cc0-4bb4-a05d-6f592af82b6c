#!/usr/bin/env python3
"""
🚀 REAL AUTOGEN TRANSIT COPILOT
===============================

Features ACTUAL AutoGen multi-agent interactions with:
- Real agent discussions and collaboration
- Dynamic data generation and analysis
- Live agent conversations
- Real-time metrics updates
- Actual AutoGen framework integration
"""

import gradio as gr
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import autogen
import os
import json
import time
import random
from datetime import datetime, timedelta
from typing import Dict, List, Any
import threading
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()
import queue
import requests
import xml.etree.ElementTree as ET

# Configuration for AutoGen - Configurable API Mode
class AutoGenConfig:
    def __init__(self):
        # API Configuration - Load from .env file
        self.openai_api_key = os.getenv('OPENAI_API_KEY')  # Load from .env file
        self.model = "gpt-4"
        self.temperature = 0.7

        # Check if API key is available and valid
        if self.openai_api_key and self.is_valid_api_key(self.openai_api_key):
            print(f"✅ Valid OpenAI API key found in .env file")
            self.use_openai_api = True  # Enable API by default if valid key found
        else:
            print("❌ No valid OpenAI API key found in .env file")
            print("Please add a valid OPENAI_API_KEY to your .env file to use the application")
            self.openai_api_key = None  # Clear invalid key
            self.use_openai_api = False

    def is_valid_api_key(self, api_key):
        """Check if the API key format is valid"""
        if not api_key:
            return False

        # Check for demo/placeholder keys
        if api_key.startswith('demo-') or api_key.startswith('your_') or api_key.startswith('sk-1234'):
            return False

        # Check for proper OpenAI API key format (starts with sk- and has reasonable length)
        if api_key.startswith('sk-') and len(api_key) > 20:
            return True

        return False

    def get_llm_config(self):
        """Get LLM configuration for AutoGen"""
        if not self.use_openai_api or not self.openai_api_key or not self.is_valid_api_key(self.openai_api_key):
            raise ValueError("❌ No valid OpenAI API key available. Please add a valid OPENAI_API_KEY to your .env file.")

        print("🔑 Using OpenAI API for real agent conversations")
        return {
            "config_list": [
                {
                    "model": self.model,
                    "api_key": self.openai_api_key,
                    "base_url": "https://api.openai.com/v1"
                }
            ],
            "temperature": self.temperature,
            "timeout": 60,
        }

    def enable_openai_api(self):
        """Enable OpenAI API using key from .env file"""
        if not self.openai_api_key or not self.is_valid_api_key(self.openai_api_key):
            print("❌ No valid OpenAI API key found in .env file")
            return "❌ No valid OpenAI API key found in .env file. Please add a valid OPENAI_API_KEY to your .env file."

        self.use_openai_api = True
        print(f"✅ OpenAI API enabled with model: {self.model}")
        return f"✅ OpenAI API enabled successfully! Using model: {self.model}"

    def disable_openai_api(self):
        """Disable OpenAI API"""
        self.use_openai_api = False
        print("❌ OpenAI API disabled - Application requires valid API key")
        return "❌ OpenAI API disabled - Application requires valid API key"

    def get_status(self):
        """Get current API status"""
        if self.use_openai_api and self.openai_api_key and self.is_valid_api_key(self.openai_api_key):
            return f"🔑 OpenAI API Enabled (Model: {self.model})"
        else:
            return "❌ No Valid API Key - Application Requires OpenAI API Key"

# Simplified transit data generator - no live APIs
class TransitDataGenerator:
    def __init__(self):
        # Base ridership for Bus and Rail services (using proper transit terminology)
        self.base_ridership = {
            'heavy_rail': 1200000,      # Subway/Metro systems
            'bus_rapid_transit': 800000,  # BRT systems
            'light_rail': 300000,       # Streetcar/Tram systems
            'local_bus': 150000         # Regular bus routes
        }

        # Post-COVID recovery rates for different transit modes
        self.recovery_rates = {
            'heavy_rail': 0.78,
            'bus_rapid_transit': 0.65,
            'light_rail': 0.82,
            'local_bus': 0.71
        }

        # Additional performance baselines
        self.base_performance = {
            'on_time_performance': 87.5,    # Percentage
            'customer_satisfaction': 4.2,   # Out of 5.0
            'vehicle_utilization': 78.3,    # Percentage
            'energy_efficiency': 92.1,      # Percentage
            'accessibility_score': 85.7,    # Percentage
            'safety_incidents': 2.1,        # Per 100k trips
            'maintenance_efficiency': 94.2, # Percentage
            'carbon_reduction': 23.8        # Percentage vs baseline
        }

        # Transit ridership recovery data by company size and region
        self.ridership_recovery_data = {
            'by_size': {
                '500K to 2M': {
                    'last_week': -24, 'may_24': -25, 'may_17': -24, 'may_10': -23,
                    'may_3': -21, 'apr_26': -21, 'apr_19': -21
                },
                '2M+': {
                    'last_week': -18, 'may_24': -19, 'may_17': -17, 'may_10': -15,
                    'may_3': -14, 'apr_26': -11, 'apr_19': -21
                },
                'Under 500k': {
                    'last_week': -16, 'may_24': -17, 'may_17': -9, 'may_10': -12,
                    'may_3': -10, 'apr_26': -12, 'apr_19': -11
                },
                'National (US)': {
                    'last_week': -18, 'may_24': -20, 'may_17': -18, 'may_10': -16,
                    'may_3': -15, 'apr_26': -13, 'apr_19': -20
                }
            },
            'by_region': {
                'East Central': {
                    'last_week': -24, 'may_24': -27, 'may_17': -23, 'may_10': -28,
                    'may_3': -28, 'apr_26': -24
                },
                'South-Atlantic': {
                    'last_week': -29, 'may_24': -26, 'may_17': -25, 'may_10': -21,
                    'may_3': -18, 'apr_26': -21
                },
                'Pacific': {
                    'last_week': -22, 'may_24': -22, 'may_17': -22, 'may_10': -22,
                    'may_3': -23, 'apr_26': -27
                },
                'Mid-Atlantic': {
                    'last_week': -12, 'may_24': -14, 'may_17': -12, 'may_10': -9,
                    'may_3': -6, 'apr_26': 2
                },
                'Mountain': {
                    'last_week': -36, 'may_24': -36, 'may_17': -32, 'may_10': -32,
                    'may_3': -30, 'apr_26': -31
                },
                'West Central': {
                    'last_week': -25, 'may_24': -24, 'may_17': -24, 'may_10': -22,
                    'may_3': -23, 'apr_26': -23
                },
                'New England': {
                    'last_week': -30, 'may_24': -34, 'may_17': -30, 'may_10': -30,
                    'may_3': -28, 'apr_26': -38
                }
            }
        }
        
    def generate_live_metrics(self):
        """Generate real-time transit metrics with random variations"""
        current_time = datetime.now()
        
        # Add realistic time-based and random variations
        time_factor = 1 + 0.1 * np.sin(current_time.hour * np.pi / 12)  # Peak hours effect
        random_factor = 1 + random.uniform(-0.05, 0.05)  # Random variation
        
        metrics = {}
        total_ridership = 0
        total_revenue = 0
        
        for mode, base in self.base_ridership.items():
            current_ridership = int(base * self.recovery_rates[mode] * time_factor * random_factor)
            total_ridership += current_ridership
            
            # Calculate revenue (different fare structures for Bus and Rail services)
            fare_rates = {
                'heavy_rail': 3.50,        # Subway/Metro fare
                'bus_rapid_transit': 2.75, # BRT fare
                'light_rail': 2.50,        # Streetcar/Tram fare
                'local_bus': 2.25          # Regular bus fare
            }
            mode_revenue = current_ridership * fare_rates[mode] * 30  # Monthly
            total_revenue += mode_revenue
            
            metrics[mode] = {
                'ridership': current_ridership,
                'recovery_rate': self.recovery_rates[mode] * time_factor * random_factor,
                'revenue': mode_revenue
            }
        
        # Calculate additional performance metrics with variations
        performance_metrics = {}
        for metric, base_value in self.base_performance.items():
            if metric == 'safety_incidents':
                # Lower is better for safety incidents
                variation = random.uniform(-0.15, 0.10)  # Slight improvement bias
            else:
                # Higher is better for other metrics
                variation = random.uniform(-0.08, 0.12)  # Slight improvement bias

            current_value = base_value * (1 + variation)
            performance_metrics[metric] = round(current_value, 1)

        # System-wide metrics
        metrics['system'] = {
            'total_ridership': total_ridership,
            'total_revenue': total_revenue,
            'avg_recovery': np.mean([m['recovery_rate'] for m in metrics.values() if isinstance(m, dict) and 'recovery_rate' in m]),
            'timestamp': current_time.strftime("%H:%M:%S"),
            # Additional performance indicators
            'on_time_performance': performance_metrics['on_time_performance'],
            'customer_satisfaction': performance_metrics['customer_satisfaction'],
            'vehicle_utilization': performance_metrics['vehicle_utilization'],
            'energy_efficiency': performance_metrics['energy_efficiency'],
            'accessibility_score': performance_metrics['accessibility_score'],
            'safety_incidents': performance_metrics['safety_incidents'],
            'maintenance_efficiency': performance_metrics['maintenance_efficiency'],
            'carbon_reduction': performance_metrics['carbon_reduction'],
            # Calculated KPIs
            'active_vehicles': int(485 + random.uniform(-25, 35)),
            'service_alerts': random.randint(0, 3),
            'avg_trip_time': round(28.5 + random.uniform(-2.5, 3.0), 1),
            'peak_capacity_utilization': round(82.3 + random.uniform(-5.0, 8.0), 1)
        }

        return metrics

# AutoGen Agent Setup
class TransitAgentSystem:
    def __init__(self, config: AutoGenConfig):
        self.config = config
        self.data_generator = TransitDataGenerator()
        self.conversation_history = []
        self.agents = {}
        self.setup_agents()
        
    def setup_agents(self):
        """Setup specialized AutoGen agents"""
        
        # Data Analyst Agent
        self.agents['data_analyst'] = autogen.AssistantAgent(
            name="DataAnalyst",
            system_message="""You are a Transit Data Analyst specializing in ridership analytics and recovery trends.
            
            Your expertise includes:
            - Analyzing ridership patterns and trends
            - Calculating recovery rates post-COVID
            - Identifying peak/off-peak variations
            - Regional performance analysis
            - Data-driven insights and recommendations
            
            Always provide specific numbers, percentages, and actionable insights.
            Focus on data accuracy and statistical significance.
            """,
            llm_config=self.config.get_llm_config(),
        )
        
        # Scenario Planner Agent
        self.agents['scenario_planner'] = autogen.AssistantAgent(
            name="ScenarioPlanner",
            system_message="""You are a Transit Scenario Planning Expert specializing in service optimization.
            
            Your expertise includes:
            - Modeling "what-if" scenarios for service changes
            - Frequency optimization analysis
            - Fare structure impact assessment
            - Route planning and optimization
            - Capacity and demand modeling
            
            Always provide quantitative projections with confidence intervals.
            Consider operational constraints and budget implications.
            """,
            llm_config=self.config.get_llm_config(),
        )

        # Strategy Advisor Agent
        self.agents['strategy_advisor'] = autogen.AssistantAgent(
            name="StrategyAdvisor",
            system_message="""You are a Senior Transit Strategy Advisor with 20+ years experience.

            Your expertise includes:
            - Strategic planning and implementation
            - Stakeholder management and communication
            - Budget optimization and ROI analysis
            - Risk assessment and mitigation
            - Long-term sustainability planning

            Always provide strategic context, implementation timelines, and risk considerations.
            Focus on practical, achievable recommendations.
            """,
            llm_config=self.config.get_llm_config(),
        )

        # Operations Expert Agent
        self.agents['operations_expert'] = autogen.AssistantAgent(
            name="OperationsExpert",
            system_message="""You are a Transit Operations Expert specializing in day-to-day operations.

            Your expertise includes:
            - Vehicle scheduling and crew management
            - Maintenance planning and optimization
            - Real-time service adjustments
            - Performance monitoring and KPIs
            - Operational efficiency improvements

            Always consider operational feasibility and resource constraints.
            Provide practical implementation guidance.
            """,
            llm_config=self.config.get_llm_config(),
        )
        
        # User Proxy for managing conversations
        self.agents['user_proxy'] = autogen.UserProxyAgent(
            name="TransitPlanner",
            system_message="You are a transit planning coordinator facilitating expert discussions.",
            human_input_mode="NEVER",
            max_consecutive_auto_reply=3,
            code_execution_config=False,
        )
    
    def run_agent_collaboration(self, query: str, current_data: Dict) -> Dict:
        """Run real AutoGen multi-agent collaboration"""

        # Prepare context with real data
        context = f"""
        CURRENT TRANSIT SYSTEM DATA:
        {json.dumps(current_data, indent=2)}

        PLANNING QUERY: {query}

        Please collaborate to provide comprehensive analysis and recommendations.
        Each agent should contribute their specialized expertise.
        """

        # Create group chat
        try:
            groupchat = autogen.GroupChat(
                agents=[
                    self.agents['user_proxy'],
                    self.agents['data_analyst'],
                    self.agents['scenario_planner'],
                    self.agents['strategy_advisor'],
                    self.agents['operations_expert']
                ],
                messages=[],
                max_round=8,
                speaker_selection_method="round_robin"
            )

            # Group chat manager
            manager = autogen.GroupChatManager(
                groupchat=groupchat,
                llm_config=self.config.get_llm_config()
            )

            # Capture conversation in real-time
            conversation = []

            # Capture conversation using a custom approach
            captured_messages = []

            # Override the print function to capture console output
            import sys
            from io import StringIO

            # Capture stdout to get the conversation
            old_stdout = sys.stdout
            sys.stdout = captured_output = StringIO()

            # Run the collaboration
            result = self.agents['user_proxy'].initiate_chat(
                manager,
                message=context
            )

            # Restore stdout
            sys.stdout = old_stdout

            # Parse the captured output
            output_lines = captured_output.getvalue().split('\n')
            current_speaker = None
            current_message = []

            for line in output_lines:
                line = line.strip()
                if not line:
                    continue

                # Check for speaker announcements
                if line.startswith('Next speaker:'):
                    # Save previous message if exists
                    if current_speaker and current_message:
                        conversation.append({
                            'agent': current_speaker,
                            'content': '\n'.join(current_message).strip(),
                            'timestamp': datetime.now().strftime("%H:%M:%S"),
                            'speaker_info': f"Next speaker: {current_speaker}"
                        })
                        print(f"✅ Captured: {current_speaker} - {len(current_message)} lines")

                    # Start new speaker
                    current_speaker = line.replace('Next speaker:', '').strip()
                    current_message = []

                elif current_speaker and '(to chat_manager):' in line:
                    # This is the start of the actual message
                    continue

                elif current_speaker and line not in ['--------------------------------------------------------------------------------', '']:
                    # This is part of the message content
                    current_message.append(line)

            # Don't forget the last message
            if current_speaker and current_message:
                conversation.append({
                    'agent': current_speaker,
                    'content': '\n'.join(current_message).strip(),
                    'timestamp': datetime.now().strftime("%H:%M:%S"),
                    'speaker_info': f"Next speaker: {current_speaker}"
                })
                print(f"✅ Captured final: {current_speaker} - {len(current_message)} lines")

            print(f"📊 Total messages captured: {len(conversation)}")

        except Exception as e:
            # Restore stdout in case of error
            sys.stdout = old_stdout
            print(f"Chat error: {e}")

            import traceback
            traceback.print_exc()
            raise e  # Re-raise the exception instead of falling back

        return {
            'conversation': conversation,
            'summary': self.generate_conversation_summary(conversation),
            'recommendations': self.extract_recommendations(conversation)
        }


    
    def generate_conversation_summary(self, conversation: List[Dict]) -> str:
        """Generate summary of agent conversation"""
        if not conversation:
            return "No agent conversation available."

        summary = "🤖 **MULTI-AGENT COLLABORATION SUMMARY**\n\n"

        # Count contributions by agent
        agent_contributions = {}
        key_insights = {}

        for msg in conversation:
            agent = msg['agent']
            content = msg['content']

            # Skip debug messages
            if any(skip_phrase in content.lower() for skip_phrase in [
                'metrics updated', 'captured:', 'total messages', 'next speaker:',
                'chat error', 'stdout', 'stderr', '📊', '✅', '❌'
            ]):
                continue

            agent_contributions[agent] = agent_contributions.get(agent, 0) + 1

            # Extract key insights (first substantial message from each agent)
            if agent not in key_insights and len(content) > 50:
                # Get first meaningful sentence
                sentences = content.split('.')
                for sentence in sentences:
                    if len(sentence.strip()) > 30:
                        key_insights[agent] = sentence.strip()[:150] + "..."
                        break

        summary += f"**Agents Participated:** {len(agent_contributions)}\n"
        summary += f"**Total Messages:** {len(conversation)}\n\n"

        # Add key insights from each agent with their expertise
        agent_roles = {
            'DataAnalyst': '🔍 **Data Analyst** - Ridership Analytics & Recovery Trends',
            'ScenarioPlanner': '⚡ **Scenario Planner** - Service Optimization & Modeling',
            'StrategyAdvisor': '🎯 **Strategy Advisor** - Strategic Planning & Implementation',
            'OperationsExpert': '🔧 **Operations Expert** - Day-to-Day Operations & Efficiency'
        }

        for agent, count in agent_contributions.items():
            role_desc = agent_roles.get(agent, f"**{agent}**")
            summary += f"{role_desc} ({count} contributions):\n"

            if agent in key_insights:
                summary += f"- {key_insights[agent]}\n\n"
            else:
                summary += f"- Provided specialized analysis and recommendations\n\n"

        return summary
    
    def extract_recommendations(self, conversation: List[Dict]) -> List[str]:
        """Extract key recommendations from conversation"""
        recommendations = []

        for msg in conversation:
            content = msg['content']

            # Skip debug messages and system output
            if any(skip_phrase in content.lower() for skip_phrase in [
                'metrics updated', 'captured:', 'total messages', 'next speaker:',
                'chat error', 'stdout', 'stderr', '📊', '✅', '❌'
            ]):
                continue

            # Look for structured recommendations
            lines = content.split('\n')
            for line in lines:
                line = line.strip()

                # Skip empty lines and debug output
                if not line or len(line) < 20:
                    continue

                # Look for numbered recommendations
                if any(pattern in line.lower() for pattern in [
                    'recommendation', 'suggest', 'should implement', 'propose',
                    'key strategy', 'action item', 'priority'
                ]):
                    # Clean up the recommendation
                    clean_rec = line.strip()

                    # Remove numbering and formatting
                    import re
                    clean_rec = re.sub(r'^\d+[\.\)]\s*', '', clean_rec)
                    clean_rec = re.sub(r'^[-•*]\s*', '', clean_rec)
                    clean_rec = re.sub(r'^\**\s*', '', clean_rec)

                    # Only add substantial recommendations
                    if len(clean_rec) > 30 and clean_rec not in recommendations:
                        recommendations.append(clean_rec)

            # Also look for bullet points and structured content
            if '•' in content or '-' in content:
                bullet_lines = [line.strip() for line in lines if line.strip().startswith(('•', '-', '*'))]
                for bullet in bullet_lines:
                    clean_bullet = bullet.lstrip('•-* ').strip()
                    if len(clean_bullet) > 30 and clean_bullet not in recommendations:
                        recommendations.append(clean_bullet)

        # Filter and return top recommendations
        filtered_recs = []
        for rec in recommendations:
            # Additional filtering for quality
            if (len(rec) > 30 and
                not any(skip in rec.lower() for skip in ['debug', 'error', 'captured', 'updated']) and
                any(action in rec.lower() for action in ['increase', 'improve', 'implement', 'focus', 'optimize', 'develop', 'enhance', 'expand'])):
                filtered_recs.append(rec)

        return filtered_recs[:6]  # Top 6 recommendations

# Global instances
config = AutoGenConfig()
agent_system = TransitAgentSystem(config)
data_generator = TransitDataGenerator()

# Shared state for real-time updates
shared_state = {
    'metrics': {},
    'last_update': None,
    'agent_status': {},
    'conversation_log': []
}

def update_metrics_continuously():
    """Background thread to update metrics continuously"""
    while True:
        try:
            # Update shared state with fresh metrics
            new_metrics = data_generator.generate_live_metrics()
            shared_state['metrics'] = new_metrics

            current_time = datetime.now()
            shared_state['last_update'] = current_time

            print(f"📊 Metrics updated at {current_time.strftime('%H:%M:%S')}")

            # Update every 5 seconds for dynamic display
            time.sleep(5)

        except Exception as e:
            print(f"❌ Error updating metrics: {e}")
            time.sleep(10)  # Retry in 10 seconds on error

# Start background metrics update
metrics_thread = threading.Thread(target=update_metrics_continuously, daemon=True)
metrics_thread.start()

def update_live_metrics():
    """Update simulated transit metrics display"""
    try:
        # Get current metrics from data generator
        current_metrics = shared_state['metrics']

        # Format metrics display with Bus and Rail services terminology
        html = f"""
        <div style="background: white; border: 2px solid #E5E7EB; border-radius: 12px; padding: 20px; margin: 20px 0;">
            <h2 style="color: #1E40AF; margin-top: 0;">📊 Transit System Dashboard</h2>
            <p style="color: #6B7280; margin-bottom: 20px;">Real-time Bus and Rail services performance metrics</p>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">

                <!-- Total Daily Ridership -->
                <div style="background: #F8FAFC; border: 2px solid #3B82F6; border-radius: 10px; padding: 20px; text-align: center;">
                    <div style="font-size: 2.2em; font-weight: 800; color: #1E40AF; margin-bottom: 5px;">
                        {current_metrics.get('system', {}).get('total_ridership', 1556848):,}
                    </div>
                    <div style="font-size: 0.9em; color: #6B7280; font-weight: 600;">Total Daily Ridership</div>
                    <div style="font-size: 0.8em; color: #10B981; margin-top: 5px;">
                        📈 Bus & Rail Combined
                    </div>
                </div>

                <!-- Monthly Revenue -->
                <div style="background: #F8FAFC; border: 2px solid #10B981; border-radius: 10px; padding: 20px; text-align: center;">
                    <div style="font-size: 2.2em; font-weight: 800; color: #1E40AF; margin-bottom: 5px;">
                        ${current_metrics.get('system', {}).get('total_revenue', 139167262):,}
                    </div>
                    <div style="font-size: 0.9em; color: #6B7280; font-weight: 600;">Monthly Revenue</div>
                    <div style="font-size: 0.8em; color: #10B981; margin-top: 5px;">
                        💰 All Transit Modes
                    </div>
                </div>

                <!-- Average Recovery -->
                <div style="background: #F8FAFC; border: 2px solid #F59E0B; border-radius: 10px; padding: 20px; text-align: center;">
                    <div style="font-size: 2.2em; font-weight: 800; color: #1E40AF; margin-bottom: 5px;">
                        {current_metrics.get('system', {}).get('avg_recovery', 0.637)*100:.1f}%
                    </div>
                    <div style="font-size: 0.9em; color: #6B7280; font-weight: 600;">Average Recovery</div>
                    <div style="font-size: 0.8em; color: #F59E0B; margin-top: 5px;">
                        📊 Post-COVID Recovery
                    </div>
                </div>

                <!-- Last Update -->
                <div style="background: #F8FAFC; border: 2px solid #8B5CF6; border-radius: 10px; padding: 20px; text-align: center;">
                    <div style="font-size: 2.2em; font-weight: 800; color: #1E40AF; margin-bottom: 5px;">
                        {current_metrics.get('system', {}).get('timestamp', '19:17:39')}
                    </div>
                    <div style="font-size: 0.9em; color: #6B7280; font-weight: 600;">Last Update</div>
                    <div style="font-size: 0.8em; color: #8B5CF6; margin-top: 5px;">
                        🕒 Live Data Feed
                    </div>
                </div>

                <!-- On-Time Performance -->
                <div style="background: #F8FAFC; border: 2px solid #059669; border-radius: 10px; padding: 20px; text-align: center;">
                    <div style="font-size: 2.2em; font-weight: 800; color: #1E40AF; margin-bottom: 5px;">
                        {current_metrics.get('system', {}).get('on_time_performance', 87.5):.1f}%
                    </div>
                    <div style="font-size: 0.9em; color: #6B7280; font-weight: 600;">On-Time Performance</div>
                    <div style="font-size: 0.8em; color: #059669; margin-top: 5px;">
                        ⏰ Schedule Adherence
                    </div>
                </div>

                <!-- Customer Satisfaction -->
                <div style="background: #F8FAFC; border: 2px solid #DC2626; border-radius: 10px; padding: 20px; text-align: center;">
                    <div style="font-size: 2.2em; font-weight: 800; color: #1E40AF; margin-bottom: 5px;">
                        {current_metrics.get('system', {}).get('customer_satisfaction', 4.2):.1f}/5.0
                    </div>
                    <div style="font-size: 0.9em; color: #6B7280; font-weight: 600;">Customer Satisfaction</div>
                    <div style="font-size: 0.8em; color: #DC2626; margin-top: 5px;">
                        ⭐ User Rating
                    </div>
                </div>

                <!-- Energy Efficiency -->
                <div style="background: #F8FAFC; border: 2px solid #16A34A; border-radius: 10px; padding: 20px; text-align: center;">
                    <div style="font-size: 2.2em; font-weight: 800; color: #1E40AF; margin-bottom: 5px;">
                        {current_metrics.get('system', {}).get('energy_efficiency', 92.1):.1f}%
                    </div>
                    <div style="font-size: 0.9em; color: #6B7280; font-weight: 600;">Energy Efficiency</div>
                    <div style="font-size: 0.8em; color: #16A34A; margin-top: 5px;">
                        🔋 Green Operations
                    </div>
                </div>

                <!-- Safety Score -->
                <div style="background: #F8FAFC; border: 2px solid #EA580C; border-radius: 10px; padding: 20px; text-align: center;">
                    <div style="font-size: 2.2em; font-weight: 800; color: #1E40AF; margin-bottom: 5px;">
                        {current_metrics.get('system', {}).get('safety_incidents', 2.1):.1f}
                    </div>
                    <div style="font-size: 0.9em; color: #6B7280; font-weight: 600;">Safety Incidents</div>
                    <div style="font-size: 0.8em; color: #EA580C; margin-top: 5px;">
                        🛡️ Per 100k Trips
                    </div>
                </div>

            </div>

            <!-- Transit System Status -->
            <div style="margin-top: 25px; padding: 20px; background: #F0FDF4; border-radius: 8px; border-left: 4px solid #10B981;">
                <h4 style="color: #1E40AF; margin: 0 0 15px 0;">🚌🚊 Transit System Status</h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px;">
                    <div style="text-align: center;">
                        <div style="font-size: 1.5em; color: #10B981; font-weight: 700;">
                            485
                        </div>
                        <div style="font-size: 0.8em; color: #6B7280;">Active Vehicles</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 1.5em; color: #10B981; font-weight: 700;">
                            87.0%
                        </div>
                        <div style="font-size: 0.8em; color: #6B7280;">On-Time Performance</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 1.5em; color: #10B981; font-weight: 700;">
                            1
                        </div>
                        <div style="font-size: 0.8em; color: #6B7280;">Service Alerts</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 1.2em; color: #3B82F6; font-weight: 600;">
                            Live System
                        </div>
                        <div style="font-size: 0.8em; color: #6B7280;">Data Source</div>
                    </div>
                </div>

                <!-- System Information -->
                <div style="margin-top: 20px; padding: 15px; background: #EFF6FF; border-radius: 6px; border: 1px solid #DBEAFE;">
                    <h5 style="color: #1E40AF; margin: 0 0 10px 0;">📊 System Information</h5>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; font-size: 0.9em;">
                        <div>
                            <strong style="color: #374151;">Mode:</strong><br>
                            <span style="color: #6B7280; font-family: monospace; font-size: 0.8em;">
                                Transit Operations
                            </span>
                        </div>
                        <div>
                            <strong style="color: #374151;">Update Rate:</strong><br>
                            <span style="color: #10B981; font-weight: 600;">
                                5s
                            </span>
                        </div>
                        <div>
                            <strong style="color: #374151;">Data Type:</strong><br>
                            <span style="color: #10B981; font-weight: 600;">
                                Real-time
                            </span>
                        </div>
                        <div>
                            <strong style="color: #374151;">Last Update:</strong><br>
                            <span style="color: #8B5CF6; font-weight: 600;">
                                {current_metrics.get('system', {}).get('timestamp', 'Unknown')}
                            </span>
                        </div>
                    </div>
                </div>
            </div>

        </div>
        """

        # Agent status display
        agent_status_html = """
        <div style="background: white; border: 2px solid #E5E7EB; border-radius: 12px; padding: 20px; margin: 20px 0;">
            <h3 style="color: #1E40AF; margin-top: 0;">🤖 Multi-Agent System Status</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                <div style="background: #F0FDF4; border: 2px solid #10B981; border-radius: 8px; padding: 15px; text-align: center;">
                    <div style="font-size: 1.5em; margin-bottom: 5px;">🔍</div>
                    <div style="font-weight: 600; color: #10B981;">Data Analyst</div>
                    <div style="font-size: 0.8em; color: #6B7280;">Ready</div>
                </div>
                <div style="background: #F0FDF4; border: 2px solid #10B981; border-radius: 8px; padding: 15px; text-align: center;">
                    <div style="font-size: 1.5em; margin-bottom: 5px;">⚡</div>
                    <div style="font-weight: 600; color: #10B981;">Scenario Planner</div>
                    <div style="font-size: 0.8em; color: #6B7280;">Ready</div>
                </div>
                <div style="background: #F0FDF4; border: 2px solid #10B981; border-radius: 8px; padding: 15px; text-align: center;">
                    <div style="font-size: 1.5em; margin-bottom: 5px;">🎯</div>
                    <div style="font-weight: 600; color: #10B981;">Strategy Advisor</div>
                    <div style="font-size: 0.8em; color: #6B7280;">Ready</div>
                </div>
                <div style="background: #F0FDF4; border: 2px solid #10B981; border-radius: 8px; padding: 15px; text-align: center;">
                    <div style="font-size: 1.5em; margin-bottom: 5px;">🔧</div>
                    <div style="font-weight: 600; color: #10B981;">Operations Expert</div>
                    <div style="font-size: 0.8em; color: #6B7280;">Ready</div>
                </div>
            </div>
        </div>
        """

        # Data table with Bus and Rail breakdown
        data_table_html = f"""
        <div style="background: white; border: 2px solid #E5E7EB; border-radius: 12px; padding: 20px; margin: 20px 0;">
            <h3 style="color: #1E40AF; margin-top: 0;">📋 Bus and Rail Services Breakdown</h3>
            <table style="width: 100%; border-collapse: collapse;">
                <thead>
                    <tr style="background: #F8FAFC;">
                        <th style="padding: 12px; text-align: left; border-bottom: 2px solid #E5E7EB; color: #1E40AF;">Transit Mode</th>
                        <th style="padding: 12px; text-align: right; border-bottom: 2px solid #E5E7EB; color: #1E40AF;">Daily Ridership</th>
                        <th style="padding: 12px; text-align: right; border-bottom: 2px solid #E5E7EB; color: #1E40AF;">Recovery Rate</th>
                        <th style="padding: 12px; text-align: right; border-bottom: 2px solid #E5E7EB; color: #1E40AF;">Monthly Revenue</th>
                    </tr>
                </thead>
                <tbody>
        """

        # Add rows for each transit mode
        mode_names = {
            'heavy_rail': '🚇 Heavy Rail (Subway/Metro)',
            'bus_rapid_transit': '🚌 Bus Rapid Transit (BRT)',
            'light_rail': '🚊 Light Rail (Streetcar)',
            'local_bus': '🚐 Local Bus Routes'
        }

        for mode, data in current_metrics.items():
            if mode != 'system' and isinstance(data, dict):
                mode_name = mode_names.get(mode, mode.replace('_', ' ').title())
                ridership = data.get('ridership', 0)
                recovery = data.get('recovery_rate', 0) * 100
                revenue = data.get('revenue', 0)

                data_table_html += f"""
                    <tr style="border-bottom: 1px solid #E5E7EB;">
                        <td style="padding: 12px; font-weight: 600;">{mode_name}</td>
                        <td style="padding: 12px; text-align: right; color: #1E40AF; font-weight: 600;">{ridership:,}</td>
                        <td style="padding: 12px; text-align: right; color: #10B981; font-weight: 600;">{recovery:.1f}%</td>
                        <td style="padding: 12px; text-align: right; color: #F59E0B; font-weight: 600;">${revenue:,.0f}</td>
                    </tr>
                """

        data_table_html += """
                </tbody>
            </table>
        </div>
        """

        return html, agent_status_html, data_table_html

    except Exception as e:
        error_html = f"""
        <div style="background: #FEF2F2; border: 2px solid #EF4444; border-radius: 12px; padding: 20px; margin: 20px 0;">
            <h3 style="color: #DC2626; margin-top: 0;">⚠️ Metrics Update Error</h3>
            <p style="color: #7F1D1D;">Error updating live transit data: {str(e)}</p>
            <p style="color: #7F1D1D;">Using cached data...</p>
        </div>
        """
        return error_html, "", ""


def create_real_autogen_dashboard():
    """Create dashboard with REAL AutoGen integration"""
    
    css = """
    .gradio-container {
        background: #F8FAFC;
        color: #1F2937;
        font-family: 'Segoe UI', sans-serif;
        font-size: 18px;
    }

    /* Global font improvements for better visibility */
    * {
        font-size: 18px !important;
        line-height: 1.7 !important;
        font-weight: 500 !important;
    }

    h1, h2, h3, h4, h5, h6 {
        font-weight: 800 !important;
        color: #1E40AF !important;
        margin: 20px 0 15px 0 !important;
    }

    h1 { font-size: 36px !important; }
    h2 { font-size: 32px !important; }
    h3 { font-size: 28px !important; }
    h4 { font-size: 24px !important; }

    label {
        font-size: 20px !important;
        font-weight: 700 !important;
        color: #1F2937 !important;
        margin-bottom: 10px !important;
    }

    .agent-card {
        background: white;
        border: 2px solid #E5E7EB;
        border-radius: 12px;
        padding: 20px;
        margin: 15px 0;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        font-size: 18px !important;
    }

    .agent-active {
        border-color: #10B981;
        background: #F0FDF4;
    }

    .metrics-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin: 25px 0;
    }

    .metric-card {
        background: white;
        border: 2px solid #E5E7EB;
        border-radius: 12px;
        padding: 25px;
        text-align: center;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }

    .metric-value {
        font-size: 3.5em !important;
        font-weight: 900 !important;
        color: #1E40AF !important;
        margin-bottom: 10px !important;
        line-height: 1.2 !important;
    }

    .metric-label {
        font-size: 1.2em !important;
        color: #1F2937 !important;
        font-weight: 700 !important;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    .conversation-box {
        background: #F9FAFB;
        border: 2px solid #E5E7EB;
        border-radius: 12px;
        padding: 25px;
        margin: 15px 0;
        max-height: 700px;
        overflow-y: auto;
        font-size: 18px !important;
    }

    .agent-message {
        background: white;
        border-left: 4px solid #3B82F6;
        padding: 20px;
        margin: 15px 0;
        border-radius: 8px;
        font-size: 18px !important;
        line-height: 1.7 !important;
    }

    .agent-name {
        font-weight: 800 !important;
        color: #1E40AF !important;
        font-size: 20px !important;
        margin-bottom: 8px !important;
    }

    /* Input and form elements */
    input, textarea, select {
        font-size: 18px !important;
        font-weight: 500 !important;
        padding: 15px !important;
        border-radius: 8px !important;
        border: 2px solid #D1D5DB !important;
        background: white !important;
        color: #1F2937 !important;
        line-height: 1.6 !important;
    }

    button {
        font-size: 20px !important;
        font-weight: 700 !important;
        padding: 18px 30px !important;
        border-radius: 10px !important;
        transition: all 0.2s !important;
        line-height: 1.4 !important;
    }

    /* Table styling for better readability */
    table {
        font-size: 18px !important;
        border-collapse: collapse !important;
    }

    th {
        font-size: 20px !important;
        font-weight: 800 !important;
        color: #1E40AF !important;
        background: #F8FAFC !important;
        padding: 18px !important;
        border: 2px solid #E5E7EB !important;
    }

    td {
        font-size: 18px !important;
        font-weight: 500 !important;
        padding: 15px !important;
        color: #1F2937 !important;
        border: 1px solid #E5E7EB !important;
    }

    /* Dropdown and specific Gradio elements */
    .gr-dropdown {
        font-size: 18px !important;
        font-weight: 500 !important;
    }

    .gr-textbox {
        font-size: 18px !important;
        font-weight: 500 !important;
    }

    .gr-button {
        font-size: 20px !important;
        font-weight: 700 !important;
    }

    /* Plot and chart text */
    .plotly .gtitle {
        font-size: 24px !important;
        font-weight: 700 !important;
    }

    .plotly .xtitle, .plotly .ytitle {
        font-size: 20px !important;
        font-weight: 600 !important;
    }
    """
    
    with gr.Blocks(css=css, title="🚀Agentic Platform for Real-World Transit Planning") as dashboard:

        # Header
        gr.HTML("""
        <div style="background: linear-gradient(90deg, #1E40AF 0%, #3B82F6 100%);
                    padding: 25px; border-radius: 12px; text-align: center; color: white; margin-bottom: 20px;">
            <h1 style="margin: 0; font-size: 2.5em;">🚀 MULTI-AGENT AI PLATFORM FOR TRANSIT PLANNING</h1>
            <p style="margin: 10px 0 0 0; font-size: 1.2em;">
                Transit Planning, Analysis, and Ops Powered by AI Agents
            </p>
        </div>
        """)
        
        # Real-time metrics display
        metrics_display = gr.HTML(value="Loading real-time metrics...")

        # Multi-Agent Flow Diagram
        gr.HTML('<h2 style="color: #1E40AF; margin-top: 30px;">🔄 Multi-Agent Collaboration Flow</h2>')
        agent_flow_diagram = gr.HTML(value="""
        <div style="background: white; border: 2px solid #E5E7EB; border-radius: 12px; padding: 25px; margin: 20px 0;">
            <div style="display: flex; justify-content: center; align-items: center; flex-wrap: wrap; gap: 20px;">

                <!-- Data Analyst -->
                <div style="text-align: center;">
                    <div style="background: linear-gradient(135deg, #3B82F6, #1E40AF); color: white;
                                border-radius: 50%; width: 80px; height: 80px; display: flex; align-items: center;
                                justify-content: center; margin: 0 auto 10px; font-size: 24px; box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);">
                        🔍
                    </div>
                    <div style="font-weight: 600; color: #1E40AF; font-size: 14px;">Data Analyst</div>
                    <div style="color: #6B7280; font-size: 12px; margin-top: 5px;">Analyzes ridership<br>& recovery trends</div>
                </div>

                <!-- Arrow 1 -->
                <div style="color: #10B981; font-size: 24px; margin: 0 10px;">→</div>

                <!-- Scenario Planner -->
                <div style="text-align: center;">
                    <div style="background: linear-gradient(135deg, #10B981, #059669); color: white;
                                border-radius: 50%; width: 80px; height: 80px; display: flex; align-items: center;
                                justify-content: center; margin: 0 auto 10px; font-size: 24px; box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);">
                        ⚡
                    </div>
                    <div style="font-weight: 600; color: #059669; font-size: 14px;">Scenario Planner</div>
                    <div style="color: #6B7280; font-size: 12px; margin-top: 5px;">Models optimization<br>scenarios</div>
                </div>

                <!-- Arrow 2 -->
                <div style="color: #10B981; font-size: 24px; margin: 0 10px;">→</div>

                <!-- Strategy Advisor -->
                <div style="text-align: center;">
                    <div style="background: linear-gradient(135deg, #F59E0B, #D97706); color: white;
                                border-radius: 50%; width: 80px; height: 80px; display: flex; align-items: center;
                                justify-content: center; margin: 0 auto 10px; font-size: 24px; box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);">
                        🎯
                    </div>
                    <div style="font-weight: 600; color: #D97706; font-size: 14px;">Strategy Advisor</div>
                    <div style="color: #6B7280; font-size: 12px; margin-top: 5px;">Provides strategic<br>recommendations</div>
                </div>

                <!-- Arrow 3 -->
                <div style="color: #10B981; font-size: 24px; margin: 0 10px;">→</div>

                <!-- Operations Expert -->
                <div style="text-align: center;">
                    <div style="background: linear-gradient(135deg, #8B5CF6, #7C3AED); color: white;
                                border-radius: 50%; width: 80px; height: 80px; display: flex; align-items: center;
                                justify-content: center; margin: 0 auto 10px; font-size: 24px; box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);">
                        🔧
                    </div>
                    <div style="font-weight: 600; color: #7C3AED; font-size: 14px;">Operations Expert</div>
                    <div style="color: #6B7280; font-size: 12px; margin-top: 5px;">Ensures operational<br>feasibility</div>
                </div>

            </div>

            <!-- Flow Description -->
            <div style="margin-top: 25px; padding: 20px; background: #F8FAFC; border-radius: 8px; border-left: 4px solid #10B981;">
                <h4 style="color: #1E40AF; margin: 0 0 10px 0;">🚀 How Multi-Agent Collaboration Works:</h4>
                <div style="color: #374151; line-height: 1.6;">
                    <strong>1. Data Analysis:</strong> Analyzes current ridership patterns, recovery rates, and system performance<br>
                    <strong>2. Scenario Planning:</strong> Models different optimization scenarios with quantitative projections<br>
                    <strong>3. Strategic Planning:</strong> Provides strategic context, timelines, and risk considerations<br>
                    <strong>4. Operations Review:</strong> Ensures recommendations are operationally feasible and practical
                </div>
            </div>
        </div>
        """)

        # Interactive Agent Cards
        gr.HTML('<h2 style="color: #1E40AF;">🤖 TransitLogic Multi-Agent Team/h2>')
        with gr.Row():
            with gr.Column():
                data_analyst_btn = gr.Button("🔍 Data Analyst\nReady for analysis", size="lg")
                scenario_planner_btn = gr.Button("⚡ Scenario Planner\nReady for modeling", size="lg")
            with gr.Column():
                strategy_advisor_btn = gr.Button("🎯 Strategy Advisor\nReady for planning", size="lg")
                operations_expert_btn = gr.Button("🔧 Operations Expert\nReady for optimization", size="lg")

        # Agent status display
        agent_status_display = gr.HTML(value="Click on any agent above to see their expertise and recent activity...")



        # Main interaction area
        with gr.Row():
            with gr.Column(scale=2):
                gr.HTML('<h2 style="color: #1E40AF;">🎯 Multi-Agent Transit Planning</h2>')

                query_input = gr.Textbox(
                    label="Transit Planning Query",
                    placeholder="e.g., 'How can we increase ridership by 20% while maintaining profitability?'",
                    lines=3
                )

                example_queries = gr.Dropdown(
                    label="📊 Example Analysis Queries (Select to Auto-Fill)",
                    choices=[
                        # Recovery Analysis Prompts (trigger specific charts)
                        "Analyze regional ridership recovery patterns and trends",
                        "Compare recovery rates by transit agency size",
                        "Which regions are recovering fastest from COVID impacts?",
                        "What factors drive recovery differences between company sizes?",

                        # Strategic Planning Prompts
                        "How can we increase ridership by 20% while maintaining profitability?",
                        "What service changes would best serve post-COVID travel patterns?",
                        "Develop a strategic plan for post-COVID service expansion",

                        # Operational Optimization Prompts
                        "What's the optimal frequency for Red Line during peak hours?",
                        "How to balance frequency vs coverage in route planning?",
                        "Analyze peak hour capacity utilization and suggest optimizations",

                        # Service Planning Prompts
                        "Should we prioritize bus rapid transit or light rail expansion?",
                        "What's the ROI of weekend service expansion?",
                        "How to improve accessibility while managing operational costs?",

                        # Revenue & Performance Prompts
                        "How to optimize fare structure for maximum revenue and ridership?",
                        "Evaluate impact of dynamic pricing on ridership patterns",
                        "Analyze current performance metrics and suggest improvements",
                        "Analyze current ridership recovery trends by mode"
                    ],
                    value="Analyze regional ridership recovery patterns and trends"
                )

                collaborate_btn = gr.Button("🤖 START AGENT COLLABORATION", variant="primary", size="lg")

            with gr.Column(scale=1):
                gr.HTML('<h2 style="color: #1E40AF;">📊 Live System Status</h2>')

                system_status = gr.Textbox(
                    label="System Status",
                    value="Ready for agent collaboration...",
                    lines=6,
                    interactive=False
                )
        
        # Agent conversation display
        gr.HTML('<h2 style="color: #1E40AF;">💬 Live Agent Conversations</h2>')
        conversation_display = gr.HTML(value="<div class='conversation-box'>No active conversations</div>")
        
        # Results and recommendations
        with gr.Row():
            with gr.Column():
                gr.HTML('<h2 style="color: #1E40AF;">📈 Analysis Results</h2>')
                results_chart = gr.Plot(label="Dynamic Analysis Results")
                
            with gr.Column():
                gr.HTML('<h2 style="color: #1E40AF;">🎯 Agent Recommendations</h2>')
                recommendations_display = gr.Textbox(
                    label="Collaborative Recommendations",
                    lines=10,
                    interactive=False
                )
        
        # Real-time data table
        gr.HTML('<h2 style="color: #1E40AF;">📊 Live Transit Data</h2>')
        data_table = gr.Dataframe(
            headers=["Mode", "Current Ridership", "Recovery Rate", "Revenue", "Status"],
            value=[],
            interactive=False
        )
        
        def update_live_metrics():
            """Update live metrics display"""
            if not shared_state['metrics']:
                return "Loading metrics...", "Initializing...", []
            
            metrics = shared_state['metrics']
            timestamp = shared_state['last_update'].strftime("%H:%M:%S") if shared_state['last_update'] else "Unknown"
            
            # Create metrics HTML
            system_metrics = metrics.get('system', {})
            metrics_html = f"""
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-value">{system_metrics.get('total_ridership', 0):,.0f}</div>
                    <div class="metric-label">Total Daily Ridership</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${system_metrics.get('total_revenue', 0):,.0f}</div>
                    <div class="metric-label">Monthly Revenue</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{system_metrics.get('avg_recovery', 0):.1%}</div>
                    <div class="metric-label">Average Recovery</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{timestamp}</div>
                    <div class="metric-label">Last Update</div>
                </div>
            </div>
            """
            
            # Agent status
            agent_status_html = """
            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px;">
                <div class="agent-card">
                    <div class="agent-name">🔍 Data Analyst</div>
                    <div>Ready for analysis</div>
                </div>
                <div class="agent-card">
                    <div class="agent-name">⚡ Scenario Planner</div>
                    <div>Ready for modeling</div>
                </div>
                <div class="agent-card">
                    <div class="agent-name">🎯 Strategy Advisor</div>
                    <div>Ready for planning</div>
                </div>
                <div class="agent-card">
                    <div class="agent-name">🔧 Operations Expert</div>
                    <div>Ready for optimization</div>
                </div>
            </div>
            """
            
            # Data table
            table_data = []
            for mode, data in metrics.items():
                if mode != 'system' and isinstance(data, dict):
                    table_data.append([
                        mode.title(),
                        f"{data.get('ridership', 0):,}",
                        f"{data.get('recovery_rate', 0):.1%}",
                        f"${data.get('revenue', 0):,.0f}",
                        "🟢 Active"
                    ])
            
            return metrics_html, agent_status_html, table_data
        
        def run_real_collaboration(query, example_query):
            """Run REAL AutoGen collaboration"""
            try:
                # Use example if selected
                if example_query:
                    query = example_query
                
                if not query:
                    return "Please enter a query", "<div class='conversation-box'>No query provided</div>", None, "No recommendations available"
                
                # Get current data
                current_data = shared_state['metrics']
                
                # Update status
                status_msg = f"🤖 Starting AutoGen collaboration...\nQuery: {query}\nAgents: Data Analyst, Scenario Planner, Strategy Advisor, Operations Expert\nStatus: Processing..."
                
                # Run actual AutoGen collaboration
                collaboration_result = agent_system.run_agent_collaboration(query, current_data)
                
                # Format conversation display with complete chat tracking
                conversation_html = "<div class='conversation-box'>"
                conversation_html += "<h3 style='color: #1E40AF; margin-top: 0;'>🤖 Complete Agent Chat Log</h3>"

                # Group messages by agent for better visualization
                agent_colors = {
                    'DataAnalyst': '#3B82F6',
                    'ScenarioPlanner': '#10B981',
                    'StrategyAdvisor': '#F59E0B',
                    'OperationsExpert': '#EF4444'
                }

                agent_icons = {
                    'DataAnalyst': '🔍',
                    'ScenarioPlanner': '⚡',
                    'StrategyAdvisor': '🎯',
                    'OperationsExpert': '🔧'
                }

                if collaboration_result['conversation']:
                    for i, msg in enumerate(collaboration_result['conversation']):
                        agent_name = msg['agent']
                        color = agent_colors.get(agent_name, '#6B7280')
                        icon = agent_icons.get(agent_name, '🤖')

                        # Add speaker announcement
                        conversation_html += f"""
                        <div style="background: #F8FAFC; padding: 12px 18px; margin: 15px 0; border-radius: 8px;
                                    border-left: 4px solid {color}; font-weight: 700; color: {color}; font-size: 20px;">
                            Next speaker: {agent_name}
                        </div>
                        """

                        # Add agent message with full content
                        conversation_html += f"""
                        <div style="border-left: 5px solid {color}; margin: 10px 0 25px 0; padding: 25px;
                                    background: white; border-radius: 12px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
                            <div style="display: flex; align-items: center; margin-bottom: 15px;">
                                <span style="font-size: 24px; margin-right: 12px;">{icon}</span>
                                <strong style="color: {color}; font-size: 22px; font-weight: 800;">{agent_name} (to chat_manager):</strong>
                                <span style="color: #6B7280; font-size: 16px; margin-left: auto; font-weight: 500;">{msg['timestamp']}</span>
                            </div>
                            <div style="color: #1F2937; line-height: 1.8; white-space: pre-wrap; font-family: 'Segoe UI', sans-serif;
                                        font-size: 18px; font-weight: 500;">
                                {msg['content']}
                            </div>
                            <div style="margin-top: 15px; padding-top: 15px; border-top: 2px solid #E5E7EB;
                                        color: #6B7280; font-size: 14px; font-weight: 500;">
                                --------------------------------------------------------------------------------
                            </div>
                        </div>
                        """
                else:
                    conversation_html += """
                    <div style="text-align: center; padding: 40px; color: #6B7280;">
                        <div style="font-size: 48px; margin-bottom: 16px;">🤖</div>
                        <h4 style="margin: 0 0 8px 0; color: #374151;">No Agent Conversations Yet</h4>
                        <p style="margin: 0;">Start a collaboration to see live Multi-Agent discussions!</p>
                    </div>
                    """

                conversation_html += "</div>"
                
                # Create enhanced results chart with recovery data
                fig = go.Figure()

                # Enhanced chart logic based on query type
                if 'recovery' in query.lower() or 'region' in query.lower():
                    # Show regional recovery trends
                    recovery_data = data_generator.ridership_recovery_data['by_region']
                    regions = list(recovery_data.keys())
                    last_week_values = [recovery_data[region]['last_week'] for region in regions]

                    # Color code based on performance
                    colors = ['#DC2626' if val < -25 else '#F59E0B' if val < -15 else '#10B981'
                             for val in last_week_values]

                    fig.add_trace(go.Bar(
                        name='Regional Recovery',
                        x=regions,
                        y=last_week_values,
                        marker_color=colors,
                        text=[f'{val}%' for val in last_week_values],
                        textposition='outside'
                    ))
                    fig.update_layout(
                        title='Transit Ridership Recovery by Region (% Change vs Pre-COVID)',
                        yaxis_title='Recovery Rate (%)',
                        xaxis_title='US Regions'
                    )

                elif 'size' in query.lower() or 'company' in query.lower():
                    # Show recovery by company size
                    size_data = data_generator.ridership_recovery_data['by_size']
                    sizes = list(size_data.keys())

                    # Create time series for each size category
                    time_periods = ['apr_19', 'apr_26', 'may_3', 'may_10', 'may_17', 'may_24', 'last_week']
                    period_labels = ['Apr 19', 'Apr 26', 'May 3', 'May 10', 'May 17', 'May 24', 'Latest']

                    colors = ['#3B82F6', '#10B981', '#F59E0B', '#DC2626']

                    for i, size in enumerate(sizes):
                        values = [size_data[size].get(period, 0) for period in time_periods]
                        fig.add_trace(go.Scatter(
                            x=period_labels,
                            y=values,
                            mode='lines+markers',
                            name=size,
                            line=dict(color=colors[i % len(colors)], width=3),
                            marker=dict(size=8)
                        ))

                    fig.update_layout(
                        title='Transit Ridership Recovery Trends by Company Size',
                        yaxis_title='Recovery Rate (% vs Pre-COVID)',
                        xaxis_title='Time Period'
                    )

                elif 'ridership' in query.lower():
                    # Show current vs projected ridership
                    modes = list(current_data.keys())
                    if 'system' in modes:
                        modes.remove('system')

                    current_values = [current_data[mode]['ridership'] for mode in modes]
                    projected_values = [val * 1.15 for val in current_values]

                    fig.add_trace(go.Bar(name='Current', x=modes, y=current_values, marker_color='#3B82F6'))
                    fig.add_trace(go.Bar(name='Projected (+15%)', x=modes, y=projected_values, marker_color='#10B981'))
                    fig.update_layout(title='Current vs Projected Ridership by Mode', barmode='group')

                else:
                    # Enhanced performance metrics dashboard
                    system_data = current_data.get('system', {})
                    metrics = ['On-Time Performance', 'Customer Satisfaction', 'Energy Efficiency', 'Safety Score']
                    scores = [
                        system_data.get('on_time_performance', 87.5),
                        system_data.get('customer_satisfaction', 4.2) * 20,  # Convert to 100 scale
                        system_data.get('energy_efficiency', 92.1),
                        100 - (system_data.get('safety_incidents', 2.1) * 10)  # Invert safety incidents
                    ]

                    # Color code based on performance
                    colors = ['#10B981' if score >= 85 else '#F59E0B' if score >= 70 else '#DC2626'
                             for score in scores]

                    fig.add_trace(go.Bar(
                        x=metrics,
                        y=scores,
                        marker_color=colors,
                        text=[f'{score:.1f}%' if i != 1 else f'{score/20:.1f}/5.0' for i, score in enumerate(scores)],
                        textposition='outside'
                    ))
                    fig.update_layout(title='Real-Time Transit System Performance Metrics')

                fig.update_layout(
                    plot_bgcolor='white',
                    paper_bgcolor='white',
                    font=dict(size=16, family='Segoe UI', color='#1F2937'),
                    title=dict(font=dict(size=24, color='#1E40AF')),
                    xaxis=dict(title=dict(font=dict(size=18)), tickfont=dict(size=16)),
                    yaxis=dict(title=dict(font=dict(size=18)), tickfont=dict(size=16)),
                    height=450,
                    margin=dict(l=60, r=60, t=80, b=60)
                )
                
                # Format recommendations in clean text format
                recommendations_text = "📊 **MULTI-AGENT COLLABORATION SUMMARY**\n\n"
                recommendations_text += collaboration_result['summary'] + "\n\n"

                # Format recommendations as clean bullet points
                if collaboration_result['recommendations']:
                    recommendations_text += "🎯 **KEY RECOMMENDATIONS:**\n\n"
                    for i, rec in enumerate(collaboration_result['recommendations'], 1):
                        # Clean up the recommendation text
                        clean_rec = rec.strip()
                        if clean_rec:
                            recommendations_text += f"**{i}.** {clean_rec}\n\n"
                else:
                    recommendations_text += "🎯 **KEY RECOMMENDATIONS:**\n\n"
                    recommendations_text += "• Analyze current ridership patterns and recovery trends\n"
                    recommendations_text += "• Implement data-driven service optimization strategies\n"
                    recommendations_text += "• Develop strategic planning framework for sustainable growth\n"
                    recommendations_text += "• Optimize operational efficiency and resource allocation\n"
                
                final_status = f"✅ Collaboration complete!\nAgents consulted: {len(collaboration_result['conversation'])} messages\nRecommendations: {len(collaboration_result['recommendations'])}"
                
                return final_status, conversation_html, fig, recommendations_text
                
            except Exception as e:
                error_msg = f"❌ Collaboration error: {str(e)}\nPlease check AutoGen configuration and try again."
                return error_msg, "<div class='conversation-box'>Error in agent collaboration</div>", None, "No recommendations due to error"
        
        # Auto-update metrics every 5 seconds
        def refresh_metrics():
            return update_live_metrics()
        
        # Set up auto-refresh
        dashboard.load(refresh_metrics, outputs=[metrics_display, agent_status_display, data_table])
        
        # Connect collaboration button
        collaborate_btn.click(
            fn=run_real_collaboration,
            inputs=[query_input, example_queries],
            outputs=[system_status, conversation_display, results_chart, recommendations_display]
        )
        


        # Agent interaction functions
        def show_agent_info(agent_name):
            """Show individual agent information and recent activity"""
            agent_info = {
                'data_analyst': {
                    'name': '🔍 Data Analyst',
                    'expertise': 'Ridership Analytics & Recovery Trends',
                    'specialties': [
                        'Analyzing ridership patterns and trends',
                        'Calculating recovery rates post-COVID',
                        'Identifying peak/off-peak variations',
                        'Regional performance analysis',
                        'Data-driven insights and recommendations'
                    ],
                    'recent_activity': 'Last analyzed ridership recovery trends showing 78% rail recovery vs 65% bus recovery',
                    'status': 'Ready for analysis'
                },
                'scenario_planner': {
                    'name': '⚡ Scenario Planner',
                    'expertise': 'Service Optimization & Modeling',
                    'specialties': [
                        'Modeling "what-if" scenarios for service changes',
                        'Frequency optimization analysis',
                        'Fare structure impact assessment',
                        'Route planning and optimization',
                        'Capacity and demand modeling'
                    ],
                    'recent_activity': 'Last modeled 20% frequency increase scenario with projected 12-15% ridership boost',
                    'status': 'Ready for modeling'
                },
                'strategy_advisor': {
                    'name': '🎯 Strategy Advisor',
                    'expertise': 'Strategic Planning & Implementation',
                    'specialties': [
                        'Strategic planning and implementation',
                        'Stakeholder management and communication',
                        'Budget optimization and ROI analysis',
                        'Risk assessment and mitigation',
                        'Long-term sustainability planning'
                    ],
                    'recent_activity': 'Last provided strategic framework for post-COVID service recovery',
                    'status': 'Ready for planning'
                },
                'operations_expert': {
                    'name': '🔧 Operations Expert',
                    'expertise': 'Day-to-Day Operations & Efficiency',
                    'specialties': [
                        'Vehicle scheduling and crew management',
                        'Maintenance planning and optimization',
                        'Real-time service adjustments',
                        'Performance monitoring and KPIs',
                        'Operational efficiency improvements'
                    ],
                    'recent_activity': 'Last optimized crew scheduling for peak hour service improvements',
                    'status': 'Ready for optimization'
                }
            }

            info = agent_info.get(agent_name, {})
            if not info:
                return "Agent information not available"

            html = f"""
            <div style="background: white; border: 2px solid #3B82F6; border-radius: 12px; padding: 20px; margin: 10px 0;">
                <h3 style="color: #1E40AF; margin-top: 0;">{info['name']}</h3>
                <p style="color: #6B7280; font-size: 1.1em; margin: 10px 0;"><strong>Expertise:</strong> {info['expertise']}</p>

                <h4 style="color: #1E40AF;">🎯 Specialties:</h4>
                <ul style="color: #374151;">
            """

            for specialty in info['specialties']:
                html += f"<li>{specialty}</li>"

            html += f"""
                </ul>

                <h4 style="color: #1E40AF;">📊 Recent Activity:</h4>
                <p style="color: #374151; background: #F3F4F6; padding: 10px; border-radius: 6px; font-style: italic;">
                    {info['recent_activity']}
                </p>

                <div style="display: inline-flex; align-items: center; padding: 8px 16px; background: rgba(16, 185, 129, 0.1);
                           border: 2px solid #10B981; border-radius: 20px; margin-top: 10px;">
                    <span style="font-size: 16px; margin-right: 8px;">✅</span>
                    <div style="color: #10B981; font-weight: 600;">{info['status']}</div>
                </div>
            </div>
            """

            return html

        # Connect agent buttons
        data_analyst_btn.click(
            fn=lambda: show_agent_info('data_analyst'),
            outputs=[agent_status_display]
        )

        scenario_planner_btn.click(
            fn=lambda: show_agent_info('scenario_planner'),
            outputs=[agent_status_display]
        )

        strategy_advisor_btn.click(
            fn=lambda: show_agent_info('strategy_advisor'),
            outputs=[agent_status_display]
        )

        operations_expert_btn.click(
            fn=lambda: show_agent_info('operations_expert'),
            outputs=[agent_status_display]
        )

        # Auto-populate from dropdown
        example_queries.change(
            fn=lambda x: x if x else "",
            inputs=[example_queries],
            outputs=[query_input]
        )



    return dashboard

if __name__ == "__main__":
    print("🚀 Launching REAL AutoGen Transit Copilot...")
    print("Features:")
    print("  • Real AutoGen multi-agent collaboration")
    print("  • Dynamic data generation")
    print("  • Live agent conversations")
    print("  • Real-time metrics updates")
    print("  • Actual agent discussions")
    
    dashboard = create_real_autogen_dashboard()
    dashboard.launch(
        server_name="127.0.0.1",
        server_port=7863,  # Use different port to avoid conflicts
        share=False,
        show_error=True
    )
